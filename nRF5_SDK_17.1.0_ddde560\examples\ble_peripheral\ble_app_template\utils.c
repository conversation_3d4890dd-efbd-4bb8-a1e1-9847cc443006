#include "utils.h"
#include "sm.h"
#include "key.h"
#include "motor.h"



APP_TIMER_DEF(auto_continuous_measure_timer_id);        //自动连续测量定时器




//中值滤波
//data：数组，必须是uint32_t格式；data_cnt：数据长度；average_cnt：按大小排列后，取中间的几个数据做平均数
//如果数据长度为
uint32_t median_filter(uint32_t* data, uint32_t data_cnt, uint32_t average_cnt)
{
    APP_ERROR_CHECK_BOOL(data_cnt >= average_cnt);
    APP_ERROR_CHECK_BOOL(data_cnt >= 1);
    
    //将数组中的数据从小到大排列
    for (uint32_t i = 0; i < data_cnt - 1; i++)
    {
        for (uint32_t j = i + 1; j < data_cnt; j++)
        {
            if (data[i] > data[j])
            {
                uint32_t temp = data[i];
                data[i] = data[j];
                data[j] = temp;
            }
        }
    }
#if 0
    //计算求平均数的起点
    if (data_cnt % 2)
    {
        average_cnt++;
    }
    uint32_t start_pos = (data_cnt - average_cnt) / 2;
    
    //求平均数
    uint32_t sum = 0;
    for (uint32_t i = 0; i < average_cnt; i++)
    {
        sum += data[start_pos++];
    }
		
    uint32_t average = sum / average_cnt;
#endif
		uint32_t sum = 0;
		for (uint32_t i = 5; i < average_cnt + 10 ; i++)
    {
        sum += data[i];
    }	
    uint32_t average = sum / average_cnt;
    return average;
}


//结果一定大于零的减法
uint32_t minite_zero(uint32_t a, uint32_t b)
{
    if (a > b)
        return a - b;
    else
        return 0;
}


//差值的绝对值
uint32_t diff_abs(uint32_t a, uint32_t b)
{
    if (a > b)
        return a - b;
    else
        return b - a;
}


//定时调用，间隔1ms
static void auto_continuous_measure_timeout_handler(void * p_context)
{
    (void) p_context;
    
    static uint32_t cnt = 0;
    
    //模拟用户按键（只有在按键启用时才发送）
    if (key_is_enabled()) {
        ui_evt_t ui_evt;
        ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
        ui_evt.evt.ui_evt_key.key_instance = (cnt % 2) ? KEY_INSTANCE_KEY_USER : KEY_INSTANCE_KEY_PRESS;//模拟用户按键或按压开关
        ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_PRESSED;
        ui_evt.evt.ui_evt_key.key_pressed_ms = 0;
        sm_event(ui_evt);
    }
    
    cnt++;
}

//自动连续测量（测试认证时使用）
void test_auto_continuous_measure_start(void)
{   
//    motor_start_once(2000);
    
    //开启定时器
    ret_code_t ret_code = app_timer_create(&auto_continuous_measure_timer_id, APP_TIMER_MODE_REPEATED, auto_continuous_measure_timeout_handler);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(auto_continuous_measure_timer_id, APP_TIMER_TICKS(2000), NULL);
    APP_ERROR_CHECK(ret_code);
}
