
#ifndef __KEY_H__
#define __KEY_H__


#include "main.h"
#include <stdint.h>
#include <stdbool.h>
#include "app_timer.h"
#include "nrf_gpio.h"


#define KEY_ANTI_SHAKE_MS           (10)
#define KEY_LONG_PRESS_START_MS     (1000)
#define KEY_LONG_PRESS_INTERVAL_MS  (300)


#define KEY_USER_PIN	GPIO_KEY_USER
#define KEY_PRESS_PIN	GPIO_KEY_PRESS
#define KEY_UP_PIN		GPIO_KEY_UP
#define KEY_DOWN_PIN	GPIO_KEY_DOWN


//typedef struct
//{
//	uint32_t key_pin; //��������
//	key_state_e key_state; //��ǰ����״̬
//	uint8_t level_last; //�ϴε����ŵ�ƽ
//  uint16_t stable_ms; //�������ŵ�ƽ�����ȶ���ʱ��
//	uint8_t long_press_sent; // �����Ƿ��Ѵ���
//	uint8_t skip_release;// �Ƿ������ɿ��¼�
//	
//} key_env_t;




//// �����¼��ص�����
//void key_event_handler(uint8_t key_id, key_event_type_t event_type);

void key_init(void);
void key_deinit(void);
void key_system_recovery(void);
void key_system_reset_recovery(void);
void key_disable_all(void);
void key_enable_all(void);
bool key_is_enabled(void);

#endif
