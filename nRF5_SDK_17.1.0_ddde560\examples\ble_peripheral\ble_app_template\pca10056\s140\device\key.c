#include "key.h"
#include "nrf_drv_gpiote.h"
#include "gui_guider.h"
#include "main.h"
#include "analog.h"
#include "sm.h"

//#include "nrf_pwr_mgmt.h"


//按键状态机
typedef enum
{
    KEY_STATE_RELEASED,         //确定松开
    KEY_STATE_MAY_PRESSED,      //可能被按下
    KEY_STATE_PRESSED,          //确认按下
    KEY_STATE_MAY_RELEASED,     //可能松开
}key_state_t;



APP_TIMER_DEF(key_user_timer_id);
APP_TIMER_DEF(key_press_timer_id);
APP_TIMER_DEF(key_up_timer_id);
APP_TIMER_DEF(key_down_timer_id);

//函数声明
void set_key_index(ui_evt_t *ui_evt,uint32_t index);

static nrfx_gpiote_pin_t m_key_pin[4] = {KEY_USER_PIN, KEY_PRESS_PIN,KEY_UP_PIN,KEY_DOWN_PIN};  //按键PIN
static app_timer_id_t m_timer_id[4];            //定时器ID
static uint32_t m_is_key_timer_runing[4] = {0}; //定时器是否在运行标志
static key_state_t m_key_state[4];              //按键状态机
static uint32_t m_key_pressed_ms[4] = {0};   //长按计时
static uint8_t key_long_press_sent[4] = {0}; // 用于长按触发时不触发短按

bool user_key_long_press_handled = false;			//用户按键长按标志位（只响应长按）
bool press_key_long_press_handled = false;		//探头按键长按标志位（只响应长按）

//自动切回主界面倒计时
extern uint32_t start_up_ms;

// 按键状态恢复机制
static uint32_t key_recovery_counter = 0;
static bool key_system_blocked = false;

//主界面（结果界面）标志位 默认umol
ui_screen_t main_screen = UI_SCREEN_RESULT1;


//开始定时器
static void key_timer_start(uint32_t index)
{
    ret_code_t err_code;
    
    if (m_is_key_timer_runing[index] == 0)
    {
        err_code = app_timer_start(m_timer_id[index], APP_TIMER_TICKS(KEY_ANTI_SHAKE_MS), NULL);
        APP_ERROR_CHECK(err_code);
        m_is_key_timer_runing[index] = 1;
    }
}
//停止定时器
static void key_timer_stop(uint32_t index)
{
    ret_code_t err_code;
    
    if (m_is_key_timer_runing[index] != 0)
    {
        err_code = app_timer_stop(m_timer_id[index]);
        APP_ERROR_CHECK(err_code);
        m_is_key_timer_runing[index] = 0;
    }
}

//重启定时器
static void key_timer_restart(uint32_t index)
{
    ret_code_t err_code;
    
    err_code = app_timer_stop(m_timer_id[index]);
    APP_ERROR_CHECK(err_code);
    err_code = app_timer_start(m_timer_id[index], APP_TIMER_TICKS(KEY_ANTI_SHAKE_MS), NULL);
    APP_ERROR_CHECK(err_code);
    m_is_key_timer_runing[index] = 1;
}

//按键中断处理
static void key_press_handler_handle(uint32_t index)
{
    uint32_t pin_value;
    
    //获取当前电平
    pin_value = nrf_gpio_pin_read(m_key_pin[index]);
    
    if (m_key_state[index] == KEY_STATE_RELEASED)//确定松开状态
    {
        if (pin_value == 0)
        {
            m_key_state[index] = KEY_STATE_MAY_PRESSED;//可能按下
            //开始定时器，一段时间后判断再确认状态
            key_timer_start(index);
        }
    }
    else if (m_key_state[index] == KEY_STATE_PRESSED)//确定按下状态
    {
        if (pin_value == 1)
        {
            m_key_state[index] = KEY_STATE_MAY_RELEASED;//可能松开
						// 立即发出释放信号
						ui_evt_t ui_evt;
						ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
						set_key_index(&ui_evt, index);
						ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_RELEASED;
						ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
						NRF_LOG_INFO("key%d released", index);
						key_system_reset_recovery(); // 重置恢复计数器
						sm_event(ui_evt);
						
            //重启定时器，一段时间后再确认状态
            key_timer_restart(index);
        }
    }
}

//设置索引所对应的按键
void set_key_index(ui_evt_t *ui_evt,uint32_t index){
	switch(index){
		case 0:
				ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_USER;
				break;
		case 1:
				ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_PRESS;
				break;
		case 2:
				ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_UP;
				break;
		case 3:
				ui_evt->evt.ui_evt_key.key_instance = KEY_INSTANCE_KEY_DOWN;
				break;
	}
}

//定时器回调
static void key_user_timeout_handler_handle(uint32_t index)
{
    uint32_t pin_value;
    
    //获取当前电平
    pin_value = nrf_gpio_pin_read(m_key_pin[index]);
    if (m_key_state[index] == KEY_STATE_MAY_PRESSED)//可能按下状态
    {
        if (pin_value == 0)
        {
            m_key_state[index] = KEY_STATE_PRESSED;//确定按下
            m_key_pressed_ms[index] = 0;
						key_long_press_sent[index] = 0; // 按下时清除长按标志
					
						// 上报按下事件 KEY_EVENT_TYPE_DOWN
            ui_evt_t ui_evt;
            ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
            set_key_index(&ui_evt, index);
            ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_DOWN;
            ui_evt.evt.ui_evt_key.key_pressed_ms = 0;
            sm_event(ui_evt);
					
            //重启定时器，使得长按的事件时间更精确
            key_timer_restart(index);
        }
        else//误检，没有按下
        {
            m_key_state[index] = KEY_STATE_RELEASED;//回到确定松开状态
            //停止定时器
            key_timer_stop(index);
        }
    }
    else if (m_key_state[index] == KEY_STATE_MAY_RELEASED)//可能松开状态
    {
        if (pin_value == 1)
        {
						m_key_state[index] = KEY_STATE_RELEASED;//确定松开
            key_timer_stop(index);
            // 只有未发生长按时才触发短按事件
            if (!key_long_press_sent[index]) {
                ui_evt_t ui_evt;
                ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                set_key_index(&ui_evt, index);
                ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_PRESSED;
                ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
                sm_event(ui_evt);
            }
            key_long_press_sent[index] = 0; // 松开时清除长按标志
						start_up_ms = 0;
        }
        else//误检，没有松开
        {
            m_key_state[index] = KEY_STATE_PRESSED;//回到按下状态
        }
    }
    else if (m_key_state[index] == KEY_STATE_PRESSED)//确定按下状态
    {
        //长按检测
        m_key_pressed_ms[index] += KEY_ANTI_SHAKE_MS;
        if ((m_key_pressed_ms[index] >= KEY_LONG_PRESS_START_MS) 
            && (m_key_pressed_ms[index] % KEY_LONG_PRESS_INTERVAL_MS == 0))
        {
            if (!key_long_press_sent[index]) {
                //上报事件：长按
                NRF_LOG_INFO("key%d long pressed", index);
                ui_evt_t ui_evt;
                ui_evt.ui_evt_type = UI_EVT_TYPE_KEY;
                ui_evt.evt.ui_evt_key.key_instance = (index == 0) ? KEY_INSTANCE_KEY_USER : KEY_INSTANCE_KEY_PRESS;
                ui_evt.evt.ui_evt_key.key_evt_type = KEY_EVENT_TYPE_LONG_PRESS;
                ui_evt.evt.ui_evt_key.key_pressed_ms = m_key_pressed_ms[index];
                sm_event(ui_evt);
                key_long_press_sent[index] = 1; // 标记已长按
            }
        }
    }
}



//user按键中断
static void key_user_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(0);
}


//press按键中断
static void key_press_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(1);
}

//up按键中断
static void key_up_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(2);
}

//down按键中断
static void key_down_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
    key_press_handler_handle(3);
}

//user定时器回调
static void key_user_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(0);
}


//press定时器回调
static void key_press_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(1);
}

//up定时器回调
static void key_up_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(2);
}

//down定时器回调
static void key_down_timeout_handler(void * p_context)
{
    key_user_timeout_handler_handle(3);
}

//获取实时按键状态
bool is_key_pressed(key_instance_t key_instance)
{
    if (m_key_state[key_instance] == KEY_STATE_PRESSED)
    {
        return true;
    }
    else
    {
        return false;
    }
}
//按键初始化
void key_init(void)
{
    ret_code_t err_code;
    
    m_timer_id[0] = key_user_timer_id;
    m_timer_id[1] = key_press_timer_id;
		m_timer_id[2] = key_up_timer_id;
		m_timer_id[3] = key_down_timer_id;
    
    //中断
    nrf_drv_gpiote_in_config_t gpiote_config = GPIOTE_CONFIG_IN_SENSE_TOGGLE(true);
    gpiote_config.pull = NRF_GPIO_PIN_PULLUP;
    
    err_code = nrf_drv_gpiote_in_init(KEY_USER_PIN, &gpiote_config, key_user_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_USER_PIN, true);
    
    err_code = nrf_drv_gpiote_in_init(KEY_PRESS_PIN, &gpiote_config, key_press_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_PRESS_PIN, true);
	
		err_code = nrf_drv_gpiote_in_init(KEY_UP_PIN, &gpiote_config, key_up_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_UP_PIN, true);
		
		err_code = nrf_drv_gpiote_in_init(KEY_DOWN_PIN, &gpiote_config, key_down_handler);
    APP_ERROR_CHECK(err_code);
    nrf_drv_gpiote_in_event_enable(KEY_DOWN_PIN, true);
    
    //定时器
    err_code = app_timer_create(&key_user_timer_id, APP_TIMER_MODE_REPEATED, key_user_timeout_handler);
    APP_ERROR_CHECK(err_code);
    
    err_code = app_timer_create(&key_press_timer_id, APP_TIMER_MODE_REPEATED, key_press_timeout_handler);
    APP_ERROR_CHECK(err_code);
		
		err_code = app_timer_create(&key_up_timer_id, APP_TIMER_MODE_REPEATED, key_up_timeout_handler);
    APP_ERROR_CHECK(err_code);
		
		err_code = app_timer_create(&key_down_timer_id, APP_TIMER_MODE_REPEATED, key_down_timeout_handler);
    APP_ERROR_CHECK(err_code);
    
    //初始状态
    for (uint32_t i = 0; i < 4; i++)
    {
        m_key_state[i] = KEY_STATE_RELEASED;
        m_is_key_timer_runing[i] = 0;
    } 
}


//按键反初始化
void key_deinit(void)
{
    nrf_gpio_cfg_input(KEY_PRESS_PIN, NRF_GPIO_PIN_NOPULL);
}

// 按键系统恢复函数
void key_system_recovery(void)
{
    key_recovery_counter++;

    // 如果检测到按键系统可能被阻塞（超过5秒没有按键响应）
    if (key_recovery_counter > 5000) {
        key_system_blocked = true;

        // 重置所有按键状态
        for (int i = 0; i < 4; i++) {
            m_key_state[i] = KEY_STATE_RELEASED;
            m_key_pressed_ms[i] = 0;
            key_long_press_sent[i] = 0;

            // 停止所有按键定时器
            if (m_is_key_timer_runing[i]) {
                app_timer_stop(m_timer_id[i]);
                m_is_key_timer_runing[i] = 0;
            }
        }

        key_recovery_counter = 0;
        key_system_blocked = false;

        NRF_LOG_INFO("Key system recovered");
    }
}

// 重置按键恢复计数器（在按键事件发生时调用）
void key_system_reset_recovery(void)
{
    key_recovery_counter = 0;
    key_system_blocked = false;
}
