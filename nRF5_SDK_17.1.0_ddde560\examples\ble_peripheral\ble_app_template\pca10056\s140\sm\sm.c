#include "sm.h"
#include "sm_power_on.h"
#include "sm_power_off.h"
#include "sm_ready.h"
#include "sm_measure.h"
#include "sm_result.h"
#include "sm_history.h"
#include "sm_calibrate.h"
#include "sm_error.h"
#include "sm_uncali.h"
#include "sm_locked.h"
#include "watchdog.h"
#include "gui_guider.h"
#include "sm_measure_mode.h"
#include "sm_measure_unit.h"
#include "sm_baby.h"
#include "key.h"


APP_TIMER_DEF(sm_tick_timer_id);        //全局tick
APP_TIMER_DEF(auto_power_off_timer_id); //自动关机计数器
static sm_t m_sm;                       //全局状态机
static sm_measure_unit_t unit_mode = RESULT1_MODE;		//当前主界面模式
static bool return_flag = 1;		//1返回 0不返回
volatile static uint32_t m_auto_power_off_time_reload;//软件运行时修改的关机时间
volatile static uint32_t m_auto_power_off_countdown;//自动关机计数器
volatile static uint32_t m_power_on_time_ms = 0;   //已开机时间


//重载自动关机定时器
void system_auto_power_off_timer_reload(void)
{
//	  NRF_LOG_INFO("T reset");
    m_auto_power_off_countdown = m_auto_power_off_time_reload;
}


//自动关机定时器回调
static void auto_power_off_timeout_handler(void * p_context)
{
//    if (m_sm != SM_FACTORY)
//    {
//        if (m_auto_power_off_countdown > 0)
//        {
//            m_auto_power_off_countdown--;
//            if (m_auto_power_off_countdown == 0)
//            {
////                sm_jump(SM_POWER_OFF, 1);//自动关机
//            }
//        }
//    }
}

//切换回主界面
void set_switch_main_screen(void){
	return_flag = 1;
}
//不切换回主界面
void reset_switch_main_screen(void){
	return_flag = 0;
}
//获得标志位
bool get_switch_main_screen(void){
	return return_flag;
}
//获取主界面单位模式
bool get_measure_unit(void){
	return unit_mode;
}
void set_measure_unit(sm_measure_unit_t unit){
	unit_mode = unit;
}

//获取全局状态机
sm_t sm_get(void)
{
    return m_sm;
}


//跳转到新状态
void sm_jump(sm_t new_sm, uint32_t para)
{
    m_sm = new_sm;  
    switch (m_sm)
    {
			case SM_READY:
				NRF_LOG_INFO("SM_READY");
				sm_ready_init();
				break;
			case SM_MEASURE:
				NRF_LOG_INFO("SM_MEASURE");
				sm_measure_init();
				break;
			case SM_MEASURE_MODE:
				NRF_LOG_INFO("SM_MEASURE_MODE");
				sm_measure_mode_init();
				break;
			case SM_MEASURE_UNIT:
				NRF_LOG_INFO("SM_MEASURE_UNIT");
				sm_measure_unit_init();
				break;
			case SM_RESULT:
				NRF_LOG_INFO("SM_RESULT");
				break;
			case SM_BABY:
				NRF_LOG_INFO("SM_BABY");
				sm_baby_init();
			break;
        default:
            break;
    }
}


//定时调用，间隔1ms
static void sm_tick_timeout_handler(void * p_context)
{
    m_power_on_time_ms++;

    // 按键系统恢复检查（每1ms调用一次）
    key_system_recovery();

    switch (m_sm)
    {
			case SM_READY:
				sm_ready_tick();
				break;
			case SM_MEASURE:
				sm_measure_tick();
				break;
			case SM_MEASURE_MODE:
				sm_measure_mode_tick();
				break;
			case SM_MEASURE_UNIT:
				sm_measure_unit_tick();
				break;
			case SM_RESULT:

				break;
			case SM_BABY:
				sm_baby_tick();
			break;
        default:
            break;
    }
}

//事件分发
void sm_event(ui_evt_t ui_evt)
{
    // 调试：记录所有事件
    if (ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) {
        NRF_LOG_INFO("sm_event: KEY event - instance=%d, type=%d, current_state=%d",
                     ui_evt.evt.ui_evt_key.key_instance,
                     ui_evt.evt.ui_evt_key.key_evt_type,
                     m_sm);
    }

    //事件分发到相应界面
    switch (m_sm)
    {
			case SM_READY:
				sm_ready_event(ui_evt);
				break;
			case SM_MEASURE:
				sm_measure_event(ui_evt);
				break;
			case SM_MEASURE_MODE:
				sm_measure_mode_event(ui_evt);
				break;
			case SM_MEASURE_UNIT:
				sm_measure_unit_event(ui_evt);
				break;
			case SM_RESULT:

				break;
			case SM_BABY:
				sm_baby_event(ui_evt);
				break;
        default:
            break;
    }
}


//状态机初始化
void sm1_init(void)
{
    ret_code_t ret_code;
    
    m_power_on_time_ms = 0;
    
    //全局tick定时器
    ret_code = app_timer_create(&sm_tick_timer_id, APP_TIMER_MODE_REPEATED, sm_tick_timeout_handler);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(sm_tick_timer_id, APP_TIMER_TICKS(1), NULL);
    APP_ERROR_CHECK(ret_code);
//    
//    //自动关机定时器
//    m_auto_power_off_time_reload = AUTO_POWER_OFF_SECOND;
//    system_auto_power_off_timer_reload();
//    ret_code = app_timer_create(&auto_power_off_timer_id, APP_TIMER_MODE_REPEATED, auto_power_off_timeout_handler);
//    APP_ERROR_CHECK(ret_code);
//    ret_code = app_timer_start(auto_power_off_timer_id, APP_TIMER_TICKS(1000), NULL);
//    APP_ERROR_CHECK(ret_code);   
//    //进入界面
    sm_jump(SM_READY, 0);
}

//运行时设置自动关机时间
void sm_set_auto_power_off_time(uint32_t second)
{
    m_auto_power_off_time_reload = second;
    system_auto_power_off_timer_reload();
    
}


//获取已开机时间
uint32_t sm_get_power_on_time(void)
{
    return m_power_on_time_ms;
}
