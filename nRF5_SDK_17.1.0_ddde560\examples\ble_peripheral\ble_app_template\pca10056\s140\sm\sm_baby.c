#include "sm_baby.h"
#include "gui.h"

static uint32_t baby_display_time_ms = 0;

void sm_baby_init(void){
	//设置当前UI界面
	set_current_gui(UI_SCREEN_BABY2S);
	//显示UI界面
	switch_to_next_screen(UI_SCREEN_BABY2S);
	//设置返回主界面定时
	set_switch_main_screen();
	//初始化显示时间计数器
	baby_display_time_ms = 0;
	// 不要立即跳转到READY状态，让系统在BABY状态停留
}

void sm_baby_tick(void){
	baby_display_time_ms++;

	// 在baby界面停留2秒后自动返回READY状态
	if (baby_display_time_ms >= 2000) {
		sm_jump(SM_READY, 0);
	}
}

void sm_baby_event(ui_evt_t ui_evt){
	// 在baby界面期间，如果用户按任意键，立即返回READY状态
	if (ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) {
		sm_jump(SM_READY, 0);
	}
}