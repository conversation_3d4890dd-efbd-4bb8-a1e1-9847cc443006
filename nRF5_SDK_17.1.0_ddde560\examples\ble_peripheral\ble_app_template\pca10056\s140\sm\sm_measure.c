#include "sm_measure.h"
#include "sm_ready.h"
#include "analog.h"
#include "motor.h"
#include "algo.h"
#include "frame.h"
#include "power.h"
#include "storage.h"
#include "gui_guider.h"
//#include "protocol.h"
#include "nrf_drv_saadc.h"
#include "saadc.h"
#include "nrf_nvic.h" 
#include "nrf_delay.h"
#include "gui_guider.h"

volatile static uint32_t sm7_tick_ms;
static uint32_t press_key_resealed = 0;//是否松开了按压按键
static uint32_t colorChannal = 0x21;//通道数   20代表不是校准模式的测量
extern lv_ui ui;

// 外部变量声明（在sm_ready.c中定义）
extern uint32_t average_times_total;
extern uint32_t average_times_now;
extern uint32_t bilirubin_average;
extern uint32_t measure_model_set;

void sm_measure_by_cmd(uint32_t        para)   //divid 
{	
	colorChannal = para;
//	NRF_LOG_INFO("colorChannal set: %d", colorChannal);
//	sm_measure_init();
}



void sm_measure_init(void)
{
    sm7_tick_ms = 0;
    // 重置测量计数器，确保每次进入测量状态时都从0开始
    average_times_now = 0;
    bilirubin_average = 0;
    NRF_LOG_INFO("SM_MEASURE_INIT: Reset counters - average_times_now=%d, bilirubin_average=%d", average_times_now, bilirubin_average);
//		page_stop_timer();    //david ++++ 0319
}


void sm_measure_tick(void)
{
    sm7_tick_ms++;
    
    if (sm7_tick_ms == 1)
    {
        //清屏，测量中不能显示动画，会卡顿
//        page_clear();
//			  nrfx_saadc_calibrate_offset();   //ADC温度自动校准
//				while (nrf_drv_saadc_is_busy());   //等待校准完成
			 
    }
    else if (sm7_tick_ms == 10)
    {
//        press_key_resealed = 0;
//        //启动一次测量
			NRF_LOG_INFO("analog_measure");
        analog_measure(1);//300ms
    }
    else if (sm7_tick_ms == 350)
    {		
        //动画：测量
//		  page_clear();
//        page_measure();//100ms
    }

    //跳转状态：SM_READY或者SM_ERROR
    else if (sm7_tick_ms == 700)
    {
        //震动
//        if (get_battery_level() >= BATTERY_LEVEL_MIDDLE)
//        {
////						NRF_LOG_INFO("motor start");
////					  NRF_LOG_PROCESS();
//						motor_start_once(100); 
//        }
        //判断是否有异常
        uint32_t algo_error_state = get_algo_error_state();
        if (press_key_resealed == 1)//算法检测到错误或者过早抬起探头algo_error_state != 0 ||
        {
//           sm_jump(SM_ERROR, 1);
        }
		else if (algo_error_state != 0)
        {
//            sm_jump(SM_ERROR, 1);
			// NVIC_SystemReset();
        }
        else
        {
			if (measure_model_set != 0 && measure_model_set != average_times_total)//需要改变测量模式
			{
				//无论是否有多次测量没测完，都直接跳转模式
				sm_jump(SM_READY, 0);
			}
			else
			{
				if (average_times_now == average_times_total)//完成测量
				{
//								NRF_LOG_INFO("ASM_RESULT");
					sm_jump(SM_RESULT, 0);
				}
				else//多次平均，还没测完
				{
//					NRF_LOG_INFO("ASM_READY");
//					sm_jump(SM_READY, 0);
				}
			}
        }
    }
}


void sm_measure_event(ui_evt_t ui_evt)
{
	NRF_LOG_INFO("sm_measure_event");
//	  uint32_t  curtempture = 0;
    //松开探头
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY)
        && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_PRESS)
        && (ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_RELEASED))
    {
        if (sm7_tick_ms < 350)//仅在测量期间判断 divid  old  350
        {
            press_key_resealed = 1;//过早抬起探头
        }
    }
	//测量完成
    if (ui_evt.ui_evt_type == UI_EVT_TYPE_MEASURE_DONE)
    {
//		curtempture = ui_evt.evt.ui_evt_measure_done.tempture;
        //调用算法
		algo_calc(ui_evt.evt.ui_evt_measure_done.dark, ui_evt.evt.ui_evt_measure_done.green, ui_evt.evt.ui_evt_measure_done.blue,ui_evt.evt.ui_evt_measure_done.tempture);
				
		//求均值
		bilirubin_average += get_bilirubin_result();
//				NRF_LOG_INFO("bilirubin average: %d", get_bilirubin_result());
		//完成测量
		NRF_LOG_INFO("Measure check: average_times_now=%d, average_times_total=%d", average_times_now, average_times_total);
		if (++average_times_now == average_times_total)
		{
				bilirubin_average /= average_times_total;
			//显示
			NRF_LOG_INFO("Measurement complete! bilirubin_average: %d, jumping to SM_READY",bilirubin_average);
			// 显示测量结果到屏幕上
			extern lv_ui ui;
			show_bilirubin(&ui, (float)bilirubin_average);
			// 强制刷新LVGL显示
			lv_task_handler();
				if (get_algo_error_state() == 0)//仅在结果正常时才
				{
//					NRF_LOG_INFO("bilirubin average: %d", bilirubin_average);
//					record_save(ui_evt.evt.ui_evt_measure_done.dark, ui_evt.evt.ui_evt_measure_done.green, ui_evt.evt.ui_evt_measure_done.blue, bilirubin_average);
				}
				sm_jump(SM_READY,0);
		}
		else
		{
			NRF_LOG_INFO("Measurement not complete yet: average_times_now=%d, average_times_total=%d", average_times_now, average_times_total);
		}
		//发送数据给APP divid add
		if(colorChannal == 0x20)
		{			
//					  NRF_LOG_INFO("result:dark:%d green:%d blue:%d get_bilirubin_result:%d temp:%d", ui_evt.evt.ui_evt_measure_done.dark,ui_evt.evt.ui_evt_measure_done.green,ui_evt.evt.ui_evt_measure_done.blue,get_bilirubin_result(),ui_evt.evt.ui_evt_measure_done.tempture);
			temprecord tmp_con_data;
			get_temp_data(&tmp_con_data);
//					protocol_ble_send_data_nochannal(get_algo_error_state(), !press_key_resealed,
//						ui_evt.evt.ui_evt_measure_done.dark, ui_evt.evt.ui_evt_measure_done.green, ui_evt.evt.ui_evt_measure_done.blue, 
//						get_bilirubin_result(), 
//					#if(DEBUG_DATA_MODE==0)
//  					average_times_total, average_times_now); //divid ---
//					#else
//					
//					  (uint8_t)(tmp_con_data.last_temp>>8), (uint8_t)(tmp_con_data.last_temp)); //温度相关调试的时候采用本行代码，注释上一行代码
//					#endif
		}
		else
		{
		
//						NRF_LOG_INFO("result:channal:%d dark:%d green:%d blue:%d get_bilirubin_result:%dtemp:%d", colorChannal,ui_evt.evt.ui_evt_measure_done.dark,ui_evt.evt.ui_evt_measure_done.green,ui_evt.evt.ui_evt_measure_done.blue,get_bilirubin_result(),ui_evt.evt.ui_evt_measure_done.tempture);
//					protocol_ble_send_data(get_algo_error_state(), !press_key_resealed, colorChannal,
//					ui_evt.evt.ui_evt_measure_done.dark, ui_evt.evt.ui_evt_measure_done.green, ui_evt.evt.ui_evt_measure_done.blue, 
//					get_bilirubin_result(), 
//					#if(DEBUG_DATA_MODE==0)
//					average_times_total, average_times_now); //divid ---
//					#else
//					(uint8_t)(curtempture>>8), (uint8_t)(curtempture)); //温度相关调试的时候采用本行代码，注释上一行代码
//					#endif
		
		}
    }
}

//获取结果
uint32_t get_result(void){
	return bilirubin_average;
}

//获取结果M
uint32_t get_resultM(void){
	return (bilirubin_average * TBIL_MOLAR_MASS) / 10.0f;
}
