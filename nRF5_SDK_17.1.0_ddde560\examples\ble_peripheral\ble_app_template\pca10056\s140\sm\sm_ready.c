#include "sm_ready.h"
#include "frame.h"
#include "sm_history.h"
#include "hide_trig.h"
#include "power.h"
#include "storage.h"
#include "utils.h"
#include "algo.h"
//#include "protocol.h"
#include "motor.h"
#include "boards.h"
#include "key.h"
#include "ST7789.h"
#include "gui.h"



volatile static uint32_t sm8_tick_ms;
//三次测量取均值
uint32_t average_times_total = 1;//表示通过几次测量计算均值
uint32_t average_times_now = 0;//目前已经得到几次数据
uint32_t bilirubin_average = 0;//用于求平均值
uint32_t measure_model_set = 0;//用于缓存APP设置的测量模式，因为正在测量中时不能直接改变
static uint32_t is_sn_showed = 0;//是否已经显示了序列号
static uint32_t sn_show_time_ms = 0;//屏幕上显示SN的时候一段时间内不接受按键


void sm_ready_init(void)
{
    sm8_tick_ms = 0;
//    uint32_t is_locked = 0;
////    storage_read_lock(&is_locked);
//    cali_para_t cali_para;
//    storage_read_cali_para(&cali_para);
//#ifdef	FIXEDCALDATA
//		cali_para.is_valid  = 1;//divid  debug
//#endif
//    /*if (cali_para.is_valid == 0)//未校准
//    {
//        sm_jump(SM_UNCALI, 0);
//        return;
//    }
//    else */if (is_locked)//已加锁
//    {
//        sm_jump(SM_LOCKED, 0);
//        return;
//    }
////    else if (get_battery_level() == BATTERY_LEVEL_EMPTY)//没电了
////    {
////        sm_jump(SM_POWER_OFF, 1);//自动关机
////        return;
////    }

//    sm8_tick_ms = 0;
//	
//	//由APP改变测量模式
//	if (measure_model_set != 0 && measure_model_set != average_times_total)//需要改变测量模式
//	{
//		average_times_total = measure_model_set;
//		average_times_now = 0;
//		bilirubin_average = 0;
//		measure_model_set = 0;
//	}
//	
//	//显示界面
//	if (average_times_total == 1)//如果是单次测量
//	{
////		page_ready();
//	}
//	else//多次求均值
//	{
////			NRF_LOG_INFO("ave = %d tot = %d",average_times_now,average_times_total);
////		  page_cali_cnt(average_times_now , average_times_total);
//	}
}


void sm_ready_tick(void)
{
    sm8_tick_ms++;
    if (sn_show_time_ms) sn_show_time_ms--;
}

void sm_ready_event(ui_evt_t ui_evt)
{
	NRF_LOG_INFO("ready_event\r\n");
    if (sn_show_time_ms > 0)
        return;

    //探头按键
    if ((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_PRESS)){
			if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_DOWN){
				//按下进入测量模式
				sm_jump(SM_MEASURE,0);
			}
			else if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED){
				//短按进入宝宝提示界面
				sm_jump(SM_BABY,0);
			}
			else if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_LONG_PRESS){
				
				
			}
    }
    //UP按键
		else if((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_UP)){
			if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED){
				//UP键短按
				sm_jump(SM_MEASURE_UNIT,0);
			}
		}
		//DOWN按键
 
    //用户按键
		else if((ui_evt.ui_evt_type == UI_EVT_TYPE_KEY) && (ui_evt.evt.ui_evt_key.key_instance == KEY_INSTANCE_KEY_USER)){
				if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_LONG_PRESS){
					//用户键长按关机
					NRF_LOG_INFO("power off\r\n");
					nrf_gpio_pin_clear(GPIO_LCD_BK); // 关闭背光
					close_LCD(); // 关屏
					nrf_gpio_cfg_sense_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);
					sd_power_system_off();
				}else if(ui_evt.evt.ui_evt_key.key_evt_type == KEY_EVENT_TYPE_PRESSED){
					//用户键短按切换测量模式
					sm_jump(SM_MEASURE_MODE,0);
				}
		}

//divid   
	#  if 0
    //隐藏关卡判断
    else if (ui_evt.ui_evt_type == UI_EVT_TYPE_HIDE_TRIG)
    {
        //1号指令：校准
        if (ui_evt.evt.ui_evt_hide_trig.command == 1)
        {
            sm_jump(SM_CALIBRATE, 2);
        }
        //2号指令：打印校准参数
        else if (ui_evt.evt.ui_evt_hide_trig.command == 2)
        {
            algo_init();
        }
		//3号指令：特殊校准
        else if (ui_evt.evt.ui_evt_hide_trig.command == 3)
        {
            sm_jump(SM_CALIBRATE, 3);
        }
        //7号指令：加锁
        else if (ui_evt.evt.ui_evt_hide_trig.command == 7)
        {
            uint32_t is_locked = 1;
            storage_update_lock(&is_locked);
			motor_start_once(100);
            sm_jump(SM_LOCKED, 0);
        }
		//9号指令：打印所有保存的测量数据
		else if (ui_evt.evt.ui_evt_hide_trig.command == 9)
		{
			record_send_all_history();
		}
		//10号指令：模拟按键进行自动连续测量
        else if (ui_evt.evt.ui_evt_hide_trig.command == 10)
        {
            test_auto_continuous_measure_start();
        }
        //连续5次按键：显示序列号
        if (ui_evt.evt.ui_evt_hide_trig.command == 0xFF)
        {
            is_sn_showed = 1;
            ble_gap_addr_t addr;
            uint32_t err_code = sd_ble_gap_addr_get(&addr);
            APP_ERROR_CHECK(err_code);
            page_serial_number(addr.addr[0] * addr.addr[1]);
            sn_show_time_ms = 1000;
        }
    }
	#endif
}



//APP设置测量模式为单次或三次平均
void sm_ready_app_set_model(uint8_t model)
{
//	if (average_times_total != model)//仅当不同时才设置
//	{
//		measure_model_set = model;
//		sm_t sm = sm_get();
//		if (sm == SM_READY || sm == SM_RESULT || sm == SM_HISTORY || sm == SM_ERROR)
//		{
//			sm_jump(SM_READY, 0);
//		}
//	}
}
