#include "gui.h"
#include "lv_port_disp.h"
#include "lvgl.h"
#include "key.h"

//https://docs.lvgl.io/master/examples.html

APP_TIMER_DEF(lvgl_timer_id);


void set_current_gui(ui_screen_t ui);
ui_screen_t get_current_gui(void);

//当前屏幕ui
static ui_screen_t current_screen = UI_SCREEN_LOG;
static void lvgl_timeout_handler(void * p_context);
extern lv_ui ui;

volatile uint32_t start_up_ms = 0;

//主界面渐变线和圆
static lv_color_t canvas_buf[GRADIENT_WIDTH * CANVAS_HEIGHT];
static lv_obj_t *canvas;
static lv_color_t bg_buf[GRADIENT_WIDTH * CANVAS_HEIGHT];  // 背景备份

static lv_obj_t *line_objs[LINE_COUNT];

#if LV_USE_LOG
//LVGL打印函数
static void lv_log_print_g_cb(const char * buf)
{

}
#endif

//GUI初始化
void gui_init(void)
{
	lv_init();

	lv_port_disp_init();
	ret_code_t ret_code = 0;
	//10ms定时器
	ret_code = app_timer_create(&lvgl_timer_id, APP_TIMER_MODE_REPEATED, lvgl_timeout_handler);
    APP_ERROR_CHECK(ret_code);
	ret_code = app_timer_start(lvgl_timer_id, APP_TIMER_TICKS(10), NULL);
    APP_ERROR_CHECK(ret_code);
		
}

//设置当前屏幕UI
void set_current_gui(ui_screen_t ui){
	current_screen = ui;
}

//获取当前屏幕UI
ui_screen_t get_current_gui(void){
	return current_screen;
}


void switch_to_next_screen(ui_screen_t current_screen) {
		lv_obj_t *old_scr = lv_scr_act();
		if (old_scr) {
			lv_obj_del(old_scr);
		}
    switch (current_screen) {
        case UI_SCREEN_LOG:
            setup_scr_log(&ui);
						lv_scr_load(ui.log);
            break;
        case UI_SCREEN_BABY2S:
            setup_scr_baby2s(&ui);
						lv_scr_load(ui.baby2s);
            break;
        case UI_SCREEN_BLUEDIS:
            setup_scr_blueDis(&ui);
						lv_scr_load(ui.blueDis);
            break;
        case UI_SCREEN_BLUETOOTHCONNECT:
            setup_scr_bluetoothConnect(&ui);
						lv_scr_load(ui.bluetoothConnect);
            break;
        case UI_SCREEN_CHARGED:
            setup_scr_charged(&ui);
						lv_scr_load(ui.charged);
            break;
        case UI_SCREEN_LOCK:
            setup_scr_lock(&ui);
						lv_scr_load(ui.lock);
            break;
        case UI_SCREEN_LOWBATTERY:
            setup_scr_lowBattery(&ui);
						lv_scr_load(ui.lowBattery);
            break;
        case UI_SCREEN_RESULT1:
            setup_scr_result1(&ui);
						
            break;
        case UI_SCREEN_RESULT1M:
            setup_scr_result1m(&ui);
						lv_scr_load(ui.result1m);
            break;
        case UI_SCREEN_SINGLE:
            setup_scr_single(&ui);
						lv_scr_load(ui.single);
            break;
        case UI_SCREEN_AVERAGE:
						setup_scr_average(&ui);
						lv_scr_load(ui.average);
            break;
        default:
            break;
    }
}

//画渐变线
void gradient_bar_init(lv_obj_t *parent) {
		//需要先创建canvas对象
		canvas = lv_canvas_create(parent);
    lv_canvas_set_buffer(canvas, canvas_buf, GRADIENT_WIDTH, CANVAS_HEIGHT, LV_IMG_CF_TRUE_COLOR);
    lv_obj_align(canvas, LV_ALIGN_BOTTOM_MID, 0, -10);

    // 填充背景透明
    lv_canvas_fill_bg(canvas, lv_color_black(), LV_OPA_TRANSP);

    // 计算线条起始 y 坐标（使其从圆中心穿过）
    int line_y_start = CANVAS_HEIGHT / 2 - GRADIENT_HEIGHT / 2;

    // 绘制渐变线
    for (int x = 0; x < GRADIENT_WIDTH; x++) {
        uint8_t r = (uint32_t)x * 255 / (GRADIENT_WIDTH - 1);
        uint8_t g = 255 - r;
        lv_color_t color = lv_color_make(r, g, 0);
        for (int y = line_y_start; y < line_y_start + GRADIENT_HEIGHT; y++) {
            lv_canvas_set_px(canvas, x, y, color);
        }
    }
}

//画自适应圆
void draw_indicator_on_canvas(uint8_t percent) {
    if (percent < 1) percent = 1;
    if (percent > 100) percent = 100;

    int x = (GRADIENT_WIDTH - 1) * (percent - 1) / 99;
    int cx = x;
    int cy = CANVAS_HEIGHT / 2;  // 圆心y = canvas中间

    // 获取当前点颜色
    lv_color_t color = lv_canvas_get_px(canvas, x, cy);

    // 重新绘制渐变线（如果频繁刷新，可优化为只第一次绘制）
    lv_canvas_fill_bg(canvas, lv_color_black(), LV_OPA_TRANSP);

    int line_y_start = CANVAS_HEIGHT / 2 - GRADIENT_HEIGHT / 2;
    for (int px = 0; px < GRADIENT_WIDTH; px++) {
        uint8_t r = (uint32_t)px * 255 / (GRADIENT_WIDTH - 1);
        uint8_t g = 255 - r;
        lv_color_t c = lv_color_make(r, g, 0);
        for (int py = line_y_start; py < line_y_start + GRADIENT_HEIGHT; py++) {
            lv_canvas_set_px(canvas, px, py, c);
        }
    }

    // 画圆（直径16）
    int radius = INDICATOR_RADIUS;
    for (int py = cy - radius; py <= cy + radius; py++) {
        for (int px = cx - radius; px <= cx + radius; px++) {
            int dx = px - cx;
            int dy = py - cy;
            if (dx * dx + dy * dy <= radius * radius) {
                lv_canvas_set_px(canvas, px, py, color);
            }
        }
    }
}


void create_empty_result_lines(lv_obj_t *parent){
    int base_x = 50;
    int base_y = 120;

    for (int i = 0; i < LINE_COUNT; i++) {
        lv_obj_t *line = lv_obj_create(parent);
        line_objs[i] = line; // 保存指针
        lv_obj_set_size(line, LINE_WIDTH, LINE_HEIGHT);
        lv_obj_set_style_radius(line, LINE_HEIGHT / 2, LV_PART_MAIN);
        lv_obj_set_style_bg_color(line, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
        lv_obj_set_style_bg_opa(line, LV_OPA_COVER, LV_PART_MAIN);
        lv_obj_clear_flag(line, LV_OBJ_FLAG_SCROLLABLE);
        lv_obj_set_pos(line, base_x + i * (LINE_WIDTH + LINE_SPACING), base_y);
    }
}

void clear_result_lines(void) {
    for (int i = 0; i < LINE_COUNT; i++) {
        if (line_objs[i]) {
            lv_obj_del(line_objs[i]);
            line_objs[i] = NULL;
        }
    }
}

//10ms定时器回调
static void lvgl_timeout_handler(void * p_context)
{
	//LVGL心跳
	lv_tick_inc(10);
	if(get_switch_main_screen() == 0)	start_up_ms = 0;
	
	if (start_up_ms < 5000 && get_switch_main_screen())
	{
		start_up_ms += 10;
		if (start_up_ms == 150)
		{
			//打开背光
			nrf_gpio_pin_write(GPIO_LCD_BK, 1);
		}
		if(start_up_ms == 2000){
			//2秒切换至主界面
			if(get_measure_unit() == RESULT1_MODE)		switch_to_next_screen(UI_SCREEN_RESULT1);
			else switch_to_next_screen(UI_SCREEN_RESULT1M);
			//停止主界面切换
			reset_switch_main_screen();
			//启用按键操作
			key_enable_all();
			NRF_LOG_INFO("主界面已显示，按键已启用");
		}
	}
}


static void draw_percent_cb(void *var, int32_t value) {
		LV_UNUSED(var);
    uint8_t percent = (uint8_t)value;


		// 恢复背景（用 memcpy 比 fill + for 更快）
    memcpy(canvas_buf, bg_buf, sizeof(canvas_buf));
    lv_obj_invalidate(canvas);  // 通知 LVGL 重绘对象
    

    // 计算位置
		if (percent < 0) percent = 0;
		if (percent > 100) percent = 100;
		int min_x = INDICATOR_RADIUS;
		int max_x = GRADIENT_WIDTH - 1 - INDICATOR_RADIUS;
		int x = min_x + (max_x - min_x) * percent / 100;
		int cx = x;
		int cy = CANVAS_HEIGHT / 2;

    // 获取当前颜色
    lv_color_t color = lv_canvas_get_px(canvas, x, cy);

    // 画圆
    int radius = INDICATOR_RADIUS;
    for (int py = cy - radius; py <= cy + radius; py++) {
        for (int px = cx - radius; px <= cx + radius; px++) {
            int dx = px - cx;
            int dy = py - cy;
            if (dx * dx + dy * dy <= radius * radius) {
                if (px >= 0 && px < GRADIENT_WIDTH && py >= 0 && py < CANVAS_HEIGHT)
                    lv_canvas_set_px(canvas, px, py, color);
            }
        }
    }
}


void draw_gradient_line_circle_anim(lv_obj_t *parent, uint8_t target_percent) {
    canvas = lv_canvas_create(parent);
    lv_canvas_set_buffer(canvas, canvas_buf, GRADIENT_WIDTH, CANVAS_HEIGHT, LV_IMG_CF_TRUE_COLOR);
    lv_obj_align(canvas, LV_ALIGN_BOTTOM_MID, 0, -10);

    // 初始化透明背景
    lv_canvas_fill_bg(canvas, lv_color_black(), LV_OPA_TRANSP);

		// 画渐变线
    int line_y_start = CANVAS_HEIGHT / 2 - GRADIENT_HEIGHT / 2;
    for (int px = 0; px < GRADIENT_WIDTH; px++) {
        uint8_t r = (uint32_t)px * 255 / (GRADIENT_WIDTH - 1);
        uint8_t g = 255 - r;
        lv_color_t c = lv_color_make(r, g, 0);
        for (int py = line_y_start; py < line_y_start + GRADIENT_HEIGHT; py++) {
            lv_canvas_set_px(canvas, px, py, c);
        }
    }
	
    // 启动动画
    lv_anim_t a;
    lv_anim_init(&a);
    lv_anim_set_var(&a, NULL);  // 不需要具体变量
    lv_anim_set_exec_cb(&a, draw_percent_cb);
    lv_anim_set_values(&a, 0, target_percent);
    lv_anim_set_time(&a, 500);  // 动画时长 500ms
    lv_anim_set_playback_time(&a, 0);  // 无回放
    lv_anim_start(&a);
		
		memcpy(bg_buf, canvas_buf, sizeof(canvas_buf));
}
